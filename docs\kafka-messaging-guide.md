# Zact Unified Backend - Kafka Messaging Guide

## Overview

The Zact Unified Backend uses Apache Kafka for asynchronous message processing, enabling scalable and reliable communication between different components of the system.

## Kafka Topics

### 1. entity-create-request
**Purpose**: Receives entity creation requests from client applications
**Producer**: Client applications, ZACT_APP
**Consumer**: Entity Create Consumer

### 2. entity-create-response
**Purpose**: Sends entity creation responses back to client applications
**Producer**: Entity Create Consumer
**Consumer**: Client applications, ZACT_APP

### 3. entity-batch-stream
**Purpose**: Handles batch synchronization data for large datasets
**Producer**: Sync services, Cron jobs
**Consumer**: Batch Stream Consumer

### 4. sync-completion-response
**Purpose**: Notifies about sync operation completion
**Producer**: Sync services
**Consumer**: Client applications, monitoring systems

## Message Structure

### Base Message Interface

```typescript
interface BaseMessage {
  messageId: string;           // Unique message identifier
  correlationId: string;       // Request correlation ID
  timestamp: number;           // Unix timestamp
  source: MessageSource;       // Message origin
  destination: MessageDestination; // Message target
  messageType: MessageType;    // REQUEST, RESPONSE, EVENT
  erpSystem: ERPSystem;        // QBO, XERO, NETSUITE
  entityOperation: {
    entityType?: EntityType;   // account, vendor, bill, etc.
    operation: OperationType;  // FETCH, CREATE, UPDATE, DELETE, SYNC
  };
  filters?: Record<string, any>; // Optional filters
  metadata?: {
    processingTimeMs?: number;
    errorType?: string;
    errorDetails?: string;
  };
  securityContext: {
    connectionId: string;      // Integration connection ID
  };
}
```

### Request Message Format

```typescript
interface RequestMessage extends BaseMessage {
  messageType: "REQUEST";
  payload: Record<string, any> | null;
  status: null;
}
```

**Example - Entity Creation Request:**
```json
{
  "messageId": "req-123e4567-e89b-12d3-a456-************",
  "correlationId": "corr-123e4567-e89b-12d3-a456-************",
  "timestamp": *************,
  "source": "ZACT_APP",
  "destination": "UNIFIED_BACKEND",
  "messageType": "REQUEST",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "vendor",
    "operation": "CREATE"
  },
  "payload": [
    {
      "name": "Acme Corporation",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "address": {
        "street": "123 Business St",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "country": "US"
      },
      "isActive": true
    }
  ],
  "status": null,
  "securityContext": {
    "connectionId": "conn-123e4567-e89b-12d3-a456-************"
  }
}
```

### Response Message Format

```typescript
interface ResponseMessage extends BaseMessage {
  messageType: "RESPONSE";
  payload: {
    data?: any;
    pagination?: Pagination;
    entityCounts?: Record<string, number>;
    message?: string;
  };
  status: {
    code: string;
    message: string;
  };
}
```

**Example - Successful Entity Creation Response:**
```json
{
  "messageId": "resp-123e4567-e89b-12d3-a456-************",
  "correlationId": "corr-123e4567-e89b-12d3-a456-************",
  "timestamp": 1642248605000,
  "source": "UNIFIED_BACKEND",
  "destination": "ZACT_APP",
  "messageType": "RESPONSE",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "vendor",
    "operation": "CREATE"
  },
  "payload": {
    "data": {
      "id": "vendor-123",
      "externalId": "qbo-vendor-456",
      "name": "Acme Corporation",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "address": {
        "street": "123 Business St",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "country": "US"
      },
      "isActive": true,
      "createdAt": "2024-01-15T10:30:05Z",
      "updatedAt": "2024-01-15T10:30:05Z"
    },
    "message": "Vendor created successfully"
  },
  "status": {
    "code": "SUCCESS",
    "message": "Entity created successfully"
  },
  "securityContext": {
    "connectionId": "conn-123e4567-e89b-12d3-a456-************"
  }
}
```

**Example - Error Response:**
```json
{
  "messageId": "resp-error-123",
  "correlationId": "corr-123e4567-e89b-12d3-a456-************",
  "timestamp": 1642248605000,
  "source": "UNIFIED_BACKEND",
  "destination": "ZACT_APP",
  "messageType": "RESPONSE",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "vendor",
    "operation": "CREATE"
  },
  "payload": {
    "message": "Validation failed for vendor creation"
  },
  "status": {
    "code": "VALIDATION_ERROR",
    "message": "Required field 'name' is missing"
  },
  "metadata": {
    "errorType": "VALIDATION_ERROR",
    "errorDetails": "Field validation failed: name is required"
  },
  "securityContext": {
    "connectionId": "conn-123e4567-e89b-12d3-a456-************"
  }
}
```

## Batch Processing Messages

### Batch Stream Message Format

Used for large dataset synchronization:

```json
{
  "messageId": "batch-123",
  "correlationId": "sync-operation-456",
  "timestamp": *************,
  "source": "UNIFIED_BACKEND",
  "destination": "UNIFIED_BACKEND",
  "messageType": "EVENT",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "vendor",
    "operation": "SYNC"
  },
  "payload": {
    "data": [
      {
        "id": "vendor-1",
        "name": "Vendor One",
        "email": "<EMAIL>"
      },
      {
        "id": "vendor-2",
        "name": "Vendor Two",
        "email": "<EMAIL>"
      }
    ],
    "batchInfo": {
      "batchNumber": 1,
      "totalBatches": 5,
      "batchSize": 100,
      "totalRecords": 500
    }
  },
  "securityContext": {
    "connectionId": "conn-123"
  }
}
```

## Sync Completion Messages

### Sync Completion Response Format

```json
{
  "messageId": "sync-complete-123",
  "correlationId": "sync-operation-456",
  "timestamp": *************,
  "source": "UNIFIED_BACKEND",
  "destination": "ZACT_APP",
  "messageType": "RESPONSE",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "ALL",
    "operation": "SYNC"
  },
  "payload": {
    "message": "Complete sync finished successfully",
    "entityCounts": {
      "vendor": 150,
      "account": 75,
      "class": 25
    },
    "summary": {
      "totalEntities": 250,
      "processingTimeMs": 45000,
      "startTime": "2024-01-15T10:30:00Z",
      "endTime": "2024-01-15T10:30:45Z"
    }
  },
  "status": {
    "code": "SYNC_COMPLETE",
    "message": "Synchronization completed successfully"
  },
  "securityContext": {
    "connectionId": "conn-123"
  }
}
```

## Message Processing Patterns

### 1. Request-Response Pattern

```mermaid
sequenceDiagram
    participant Client as Client App
    participant RequestTopic as entity-create-request
    participant Consumer as Entity Consumer
    participant ResponseTopic as entity-create-response

    Client->>RequestTopic: Publish request message
    RequestTopic->>Consumer: Consume message
    Consumer->>Consumer: Process entity creation
    Consumer->>ResponseTopic: Publish response message
    ResponseTopic->>Client: Consume response
```

### 2. Batch Processing Pattern

```mermaid
sequenceDiagram
    participant Sync as Sync Service
    participant BatchTopic as entity-batch-stream
    participant Consumer as Batch Consumer
    participant CompletionTopic as sync-completion-response

    Sync->>BatchTopic: Publish batch 1
    Sync->>BatchTopic: Publish batch 2
    Sync->>BatchTopic: Publish batch N
    
    BatchTopic->>Consumer: Process batch 1
    BatchTopic->>Consumer: Process batch 2
    BatchTopic->>Consumer: Process batch N
    
    Consumer->>CompletionTopic: Publish completion message
```

## Error Handling in Messages

### Error Types

1. **VALIDATION_ERROR**: Data validation failures
2. **API_ERROR**: External API call failures
3. **DATABASE_ERROR**: Database operation failures
4. **NETWORK_ERROR**: Network connectivity issues
5. **TIMEOUT_ERROR**: Operation timeout
6. **AUTHENTICATION_ERROR**: Token or auth issues

### Error Message Structure

```json
{
  "status": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed"
  },
  "metadata": {
    "errorType": "VALIDATION_ERROR",
    "errorDetails": "Required fields missing: name, email"
  },
  "payload": {
    "message": "Entity validation failed",
    "validationErrors": [
      {
        "field": "name",
        "message": "Name is required"
      },
      {
        "field": "email",
        "message": "Valid email is required"
      }
    ]
  }
}
```

## Consumer Configuration

### Entity Create Consumer

```typescript
const consumerConfig = {
  groupId: 'zact-unified-backend-group',
  sessionTimeout: 30000,
  heartbeatInterval: 3000,
  maxBytesPerPartition: 1048576, // 1MB
  maxWaitTimeInMs: 5000
};
```

### Message Processing Flow

1. **Message Consumption**: Consumer receives message from topic
2. **Validation**: Validate message structure and payload
3. **Processing**: Execute business logic (API calls, data transformation)
4. **Database Operations**: Save/update entities in database
5. **Response Generation**: Create response message
6. **Response Publishing**: Send response to appropriate topic

## Best Practices

### Message Design
- Use correlation IDs for request tracing
- Include comprehensive error information
- Keep message payloads under 1MB
- Use batch processing for large datasets

### Error Handling
- Always include error context in messages
- Use standardized error codes
- Implement retry mechanisms for transient failures
- Log all message processing activities

### Performance
- Use message batching for high-throughput scenarios
- Implement connection pooling for database operations
- Monitor message processing times
- Use appropriate partition strategies

This guide provides comprehensive information about Kafka messaging patterns and data formats used in the Zact Unified Backend system.
