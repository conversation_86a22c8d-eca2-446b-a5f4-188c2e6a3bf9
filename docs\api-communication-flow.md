# API Communication Flow - Authenctication & API Flow Documentation

The Zact Unified Backend acts as a middleware layer between client applications and external accounting systems like QuickBooks Online (QBO). The system follows OAuth 2.0 authorization code flow for secure authentication and uses API Gateway pattern for external service communication.

**Key Concepts:**

- **OAuth 2.0 Flow**: Secure token-based authentication without exposing user credentials
- **API Gateway Pattern**: Centralized external API communication with unified error handling
- **Token Management**: Automatic refresh and secure storage of access tokens
- **Unified Data Format**: Transform external API responses to consistent internal format

## Authentication Flow (API-to-API)

### 1. OAuth Initiation

```
Frontend → Zact Backend → QuickBooks OAuth → Frontend
```

**API Call:**

```http
GET /api/connect/qbo
Headers: zactcompanyid: "company-123"

Response:
{
  "data": {
    "authUri": "https://appcenter.intuit.com/connect/oauth2?..."
  }
}
```

Frontend redirects the user automatically to the authUri received in the response to initiate the QuickBooks OAuth flow.

### 2. OAuth Callback & Token Exchange

```
QuickBooks → Zact Backend → QuickBooks Token API → Database → Frontend Redirect
```

**API Calls:**

```http
# 1. QBO calls our callback
GET /api/qbo/callback?code=AUTH_CODE&realmId=COMPANY_ID&state=company-123

# 2. Backend exchanges code for tokens
POST https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
Body: {
  "grant_type": "authorization_code",
  "code": "AUTH_CODE",
  "redirect_uri": "http://localhost:8080/api/qbo/callback"
}

Response: {
  "access_token": "ACCESS_TOKEN",
  "refresh_token": "REFRESH_TOKEN",
  "expires_in": 3600
}

# 3. Backend stores tokens and redirects frontend
Redirect: http://localhost:4200?erpSystem=QBO&realmId=COMPANY_ID
```

## Key Message After Authentication

### Connection Success Response

**Sent to Frontend via Backend redirect:**

```
Backend Response: HTTP 302 Redirect
Location: http://localhost:4200?erpSystem=QBO&realmId=COMPANY_ID
```

**Theory**: After successful OAuth token exchange, the backend redirects the user back to the frontend application with connection parameters. This completes the authentication flow and notifies the frontend that the integration is established.

## Token Refresh Flow (API-to-API)

When access token expires:

```
Zact Backend → QuickBooks Token API → Database → Retry Original Call
```

**API Call:**

```http
POST https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer

Response: {
  "access_token": "NEW_ACCESS_TOKEN",
  "refresh_token": "NEW_REFRESH_TOKEN",
  "expires_in": 3600
}
```

**Theory**: OAuth tokens have limited lifespans for security. When an API call fails with 401 Unauthorized, the system automatically attempts to refresh the token using the stored refresh token. This ensures uninterrupted service without requiring user re-authentication.

### Disconnect Integration

Disconnects the current accounting platform integration.

```http
POST /disconnect
```

**Headers:**

- `zactcompanyid` (required): Organization identifier

**Response:**

```json
{
  "responseStatus": 200,
  "message": "Successfully disconnected from QBO",
  "data": {
    "connectionStatus": "DISCONNECTED",
    "disconnectedAt": "2024-01-15T10:30:00Z"
  }
}

## Complete Flow Summary

The authentication and API communication follows this pattern:

1. **OAuth Initiation**: Frontend requests OAuth URL from backend
2. **User Authorization**: User authorizes application on QuickBooks
3. **Token Exchange**: Backend exchanges authorization code for access tokens
4. **Storage & Redirect**: Backend stores tokens and redirects user to frontend
5. **Data Synchronization**: Backend fetches and transforms data from QuickBooks
6. **Unified Storage**: Transformed data is stored in unified format

## Key Points

1. **Authentication**: OAuth 2.0 with token exchange
2. **Backend Redirect**: Backend controls the redirect back to frontend after authentication
3. **API Communication**: Via API Gateway for external calls
4. **Token Management**: Automatic refresh when expired
5. **Data Transformation**: QBO format → Unified format → Database storage
6. **Theory-Driven Design**: Each component serves a specific architectural purpose
```
