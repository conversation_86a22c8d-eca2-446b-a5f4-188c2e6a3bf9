# Zact Unified Backend - API Communication Flow

## Core API Communication Patterns

### 1. Authentication Flow (API-to-API)

```
Frontend → Zact Backend → QuickBooks API → Zact Backend → Frontend
```

**Step-by-step API calls:**

1. **OAuth Initiation**

   ```
   GET /api/connect/qbo
   Headers: zactcompanyid
   → Returns: OAuth URL for QBO
   ```

2. **OAuth Callback** (QBO → Zact Backend)

   ```
   GET /api/qbo/callback?code&realmId&state
   → Zact Backend calls QBO Token API
   → Stores tokens in database
   → Redirects frontend with success
   ```

3. **Token Exchange** (Zact Backend → QBO API)
   ```
   POST https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
   Body: code, client_id, client_secret, redirect_uri
   Response: access_token, refresh_token, expires_in
   ```

### 2. Two Key Messages After Authentication

#### Message 1: Connection Success

```json
{
  "status": "CONNECTED",
  "message": "Successfully connected to QuickBooks Online",
  "data": {
    "erpSystem": "QBO",
    "realmId": "company-123",
    "companyName": "Acme Corp",
    "connectedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### Message 2: Background Sync Started

```json
{
  "messageType": "SYNC_INITIATED",
  "correlationId": "sync-456",
  "payload": {
    "message": "Background sync started for all entities",
    "entities": ["ACCOUNT", "VENDOR", "CLASS"],
    "estimatedTime": "5-10 minutes"
  },
  "status": "IN_PROGRESS"
}
```

### 3. API-to-API Communication During Sync

```
Zact Backend → API Gateway → QuickBooks API → API Gateway → Zact Backend
```

**Sync Flow:**

1. **Fetch Entities** (Zact → QBO via API Gateway)

   ```
   GET /api-gateway?service=qbo&entity=vendor&accessToken=xxx&companyId=123
   ```

2. **Process Response** (QBO → Zact via API Gateway)

   ```json
   {
     "Vendor": [
       { "Id": "1", "Name": "Vendor A", "Active": true },
       { "Id": "2", "Name": "Vendor B", "Active": false }
     ]
   }
   ```

3. **Transform & Save** (Zact Backend)
   - Maps QBO format → Unified format
   - Saves to database
   - Sends Kafka message

### 4. Complete Authentication + Sync Flow

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant BE as Zact Backend
    participant QBO as QuickBooks API
    participant DB as Database
    participant Kafka as Kafka

    Note over FE,Kafka: Authentication Phase
    FE->>BE: GET /api/connect/qbo
    BE->>FE: OAuth URL
    FE->>QBO: User authorizes
    QBO->>BE: Callback with code
    BE->>QBO: Exchange code for tokens
    QBO->>BE: access_token, refresh_token
    BE->>DB: Store integration

    Note over FE,Kafka: Success Message
    BE->>FE: Message 1: "Successfully connected"

    Note over FE,Kafka: Background Sync Phase
    BE->>Kafka: Message 2: "Background sync started"
    BE->>QBO: Fetch entities (via API Gateway)
    QBO->>BE: Entity data
    BE->>DB: Save unified data
    BE->>Kafka: Sync completion message
```

### 5. Token Refresh (API-to-API)

When tokens expire during API calls:

```
Zact Backend → QBO Token API → Zact Backend → QBO Data API
```

**Flow:**

1. API call fails with 401 Unauthorized
2. Zact Backend calls QBO refresh endpoint
3. Updates tokens in database
4. Retries original API call

## Key API Endpoints

### Authentication

- `GET /api/connect/qbo` - Start OAuth
- `GET /api/qbo/callback` - Handle OAuth callback
- `POST /api/disconnect` - Disconnect integration

### Sync Operations

- `POST /api/sync/instant/:entityType` - Manual sync
- `GET /api/service` - Generic entity operations

## Message Types

### 1. Connection Success (HTTP Response)

Sent immediately after OAuth completion

### 2. Sync Initiated (Kafka Message)

Sent to `entity-batch-stream` topic when background sync starts

### 3. Sync Completion (Kafka Message)

Sent to `sync-completion-response` topic when sync finishes

### Prerequisites

- Node.js 18+ and npm
- MySQL database
- Kafka cluster (local or remote)
- QuickBooks Online developer account

### Environment Setup

1. **Clone and Install**

   ```bash
   git clone <repository-url>
   cd Zact_Unified_backend
   npm install
   ```

2. **Environment Configuration**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**

   ```bash
   npx prisma migrate dev
   npx prisma generate
   ```

4. **Start Services**

   ```bash
   # Start Kafka (if running locally)
   # Start MySQL database

   # Start the application
   npm run dev
   ```

### Basic Usage Flow

1. **Initiate OAuth Connection**

   ```bash
   curl -X GET "http://localhost:8080/api/connect/qbo" \
     -H "zactcompanyid: your-company-id"
   ```

2. **Complete OAuth Flow**

   - Redirect user to returned `authUri`
   - User authorizes application
   - System handles callback automatically

3. **Perform Operations**
   ```bash
   # Instant sync
   curl -X POST "http://localhost:8080/api/sync/instant/VENDOR" \
     -H "zactcompanyid: your-company-id"
   ```

## 🏗️ System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │  Zact Backend   │    │ External APIs   │
│                 │    │                 │    │                 │
│ • Frontend      │◄──►│ • Express API   │◄──►│ • QuickBooks    │
│ • Mobile        │    │ • Auth Service  │    │ • Other ERPs    │
│ • Third-party   │    │ • Kafka System  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Infrastructure  │
                       │                 │
                       │ • MySQL DB      │
                       │ • Kafka Cluster │
                       │ • Monitoring    │
                       └─────────────────┘
```

## 🔐 Security Features

- **OAuth 2.0 Authentication**: Secure token-based authentication
- **Token Management**: Automatic refresh with race condition prevention
- **Request Validation**: Comprehensive input validation and sanitization
- **Error Sanitization**: No sensitive data exposed in error responses
- **Audit Logging**: Complete request and response logging
- **Rate Limiting**: Protection against abuse and DoS attacks

## 📊 Monitoring & Observability

- **Prometheus Metrics**: Request duration, counts, and custom metrics
- **Database Logging**: All API requests and Kafka messages logged
- **Error Tracking**: Comprehensive error logging and classification
- **Health Checks**: System health monitoring endpoints
- **Request Tracing**: Correlation IDs for request tracking

## 🔄 Data Flow Patterns

### 1. Synchronous API Calls

```
Client → Express → Auth → Service → External API → Database → Response
```

### 2. Asynchronous Kafka Processing

```
Client → Kafka Topic → Consumer → Validation → Mapping → API → Database → Response Topic
```

### 3. Batch Synchronization

```
Scheduler → Sync Service → Batch Messages → Consumer → Database → Completion Message
```

## 🛠️ Development Guidelines

### Code Organization

- **Services**: Business logic and external integrations
- **Routes**: API endpoint definitions
- **Middleware**: Request processing and validation
- **Utils**: Helper functions and utilities
- **Kafka**: Message producers and consumers

### Error Handling

- Use `ApiException` for all business logic errors
- Implement proper error logging and monitoring
- Return sanitized error responses to clients
- Use correlation IDs for error tracing

### Testing

- Unit tests for business logic
- Integration tests for API endpoints
- Kafka message testing
- Database operation testing

## 📈 Performance Considerations

- **Connection Pooling**: Database and HTTP connections
- **Batch Processing**: Large dataset handling
- **Caching**: Token caching and validation
- **Rate Limiting**: API protection
- **Message Batching**: Kafka optimization

## 🔧 Configuration Management

All configuration is managed through environment variables:

- Server settings (port, environment)
- Database connection strings
- OAuth credentials
- Kafka configuration
- API Gateway settings
- Monitoring and logging settings

## 📞 Support & Troubleshooting

### Common Issues

1. **Authentication Failures**: Check OAuth credentials and token expiration
2. **Kafka Connection Issues**: Verify Kafka broker connectivity
3. **Database Errors**: Check connection strings and permissions
4. **API Gateway Timeouts**: Review timeout settings and external service status

### Debugging

- Check application logs for detailed error information
- Use correlation IDs to trace requests across components
- Monitor Prometheus metrics for performance insights
- Review database logs for query performance

### Getting Help

- Review this documentation thoroughly
- Check application logs and error messages
- Use monitoring dashboards for system insights
- Contact the development team for complex issues

---

This documentation is maintained alongside the codebase and should be updated when system changes are made.
