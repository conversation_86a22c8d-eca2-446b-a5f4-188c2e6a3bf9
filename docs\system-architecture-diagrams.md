# Zact Unified Backend - System Architecture Diagrams

## High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Frontend App]
        B[External API Clients]
    end
    
    subgraph "Zact Unified Backend"
        C[Express Server]
        D[Authentication Service]
        E[API Gateway Service]
        F[Kafka Producers/Consumers]
        G[Database Layer]
    end
    
    subgraph "External Services"
        H[QuickBooks Online API]
        I[Other ERP Systems]
    end
    
    subgraph "Infrastructure"
        J[Kafka Cluster]
        K[MySQL Database]
        L[Monitoring/Logging]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    F --> J
    E --> H
    E --> I
    G --> K
    C --> L
```

## Detailed Authentication Flow

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant BE as Backend
    participant QBO as QuickBooks Online
    participant DB as Database
    participant Cache as Token Cache

    Note over FE,DB: OAuth 2.0 Authorization Code Flow
    
    FE->>BE: GET /api/connect/qbo<br/>Header: zactcompanyid
    BE->>BE: Validate company ID
    BE->>BE: Generate OAuth URL with state
    BE->>FE: Return authorization URL
    
    FE->>QBO: Redirect to OAuth URL
    Note over FE,QBO: User authorizes application
    QBO->>BE: GET /api/qbo/callback<br/>?code&realmId&state
    
    BE->>QBO: POST /oauth2/v1/tokens/bearer<br/>Exchange code for tokens
    QBO->>BE: access_token, refresh_token, expires_in
    
    BE->>DB: Upsert integration record
    BE->>Cache: Store token with expiration
    BE->>FE: Redirect with success params
    
    Note over FE,Cache: Subsequent API Calls
    
    FE->>BE: API Request with zactcompanyid
    BE->>Cache: Check token validity
    
    alt Token Valid
        Cache->>BE: Return valid token
    else Token Expired
        BE->>QBO: Refresh token request
        QBO->>BE: New tokens
        BE->>DB: Update token data
        BE->>Cache: Update cached token
    end
    
    BE->>QBO: API call with valid token
    QBO->>BE: API response
    BE->>FE: Processed response
```

## Entity Creation Flow (Kafka-based)

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Producer as Kafka Producer
    participant Topic1 as entity-create-request
    participant Consumer as Entity Consumer
    participant Validator as Validation Service
    participant Mapper as Data Mapper
    participant Gateway as API Gateway
    participant QBO as QuickBooks Online
    participant DB as Database
    participant Topic2 as entity-create-response

    Client->>Producer: Send entity creation request
    Producer->>Topic1: Publish message
    Topic1->>Consumer: Consume message
    
    Consumer->>Validator: Validate unified format
    
    alt Validation Fails
        Consumer->>Topic2: Publish validation error
        Topic2->>Client: Error response
    else Validation Passes
        Consumer->>Mapper: Map to QBO format
        Consumer->>Gateway: POST entity data
        Gateway->>QBO: Create entity API call
        QBO->>Gateway: Entity created response
        Gateway->>Consumer: QBO entity data
        Consumer->>Mapper: Map QBO response to unified
        Consumer->>DB: Save unified entity
        Consumer->>Topic2: Publish success response
        Topic2->>Client: Success response with entity
    end
```

## Middleware Processing Chain

```mermaid
flowchart TD
    A[Incoming Request] --> B[Security Headers]
    B --> C[CORS Middleware]
    C --> D[Body Parser]
    D --> E[Request Tracing]
    E --> F[API Logger Start]
    F --> G[Prometheus Metrics]
    G --> H[Async Handler Wrapper]
    
    H --> I[Route Handler]
    I --> J[Business Logic]
    J --> K[Service Layer]
    K --> L[External API Calls]
    L --> M[Database Operations]
    
    M --> N[Response Processing]
    N --> O[API Logger End]
    O --> P[Error Handler]
    P --> Q[Response Sent]
    
    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style P fill:#ffcdd2
```

## Token Management Architecture

```mermaid
graph TB
    subgraph "Token Management System"
        A[Token Request] --> B{Token Exists?}
        B -->|Yes| C{Token Valid?}
        B -->|No| D[Fetch from Database]
        
        C -->|Yes| E[Return Token]
        C -->|No| F{Refresh in Progress?}
        
        F -->|Yes| G[Wait for Refresh]
        F -->|No| H[Start Refresh Process]
        
        H --> I[Lock Token Refresh]
        I --> J[Call QBO Refresh API]
        J --> K[Update Database]
        K --> L[Update Cache]
        L --> M[Release Lock]
        M --> E
        
        G --> E
        D --> C
    end
    
    subgraph "Concurrency Control"
        N[Request 1] --> F
        O[Request 2] --> F
        P[Request 3] --> F
    end
    
    style I fill:#ffeb3b
    style M fill:#4caf50
```

## Database Schema Overview

```mermaid
erDiagram
    AccountingPlatformIntegration {
        string id PK
        string zactCompanyId
        string accountingPlatformType
        string externalCompanyId
        string companyName
        enum connectionStatus
        json authentication
        datetime lastConnectedAt
        datetime createdAt
        datetime updatedAt
    }
    
    RequestLog {
        string id PK
        string sourceId
        string connectionId FK
        enum logType
        string endpointOrTopic
        string methodOrAction
        json details
        int status
        string message
        datetime executionTime
        datetime createdAt
    }
    
    UnifiedVendor {
        string id PK
        string connectionId FK
        string externalId
        string name
        string email
        json address
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    UnifiedBill {
        string id PK
        string connectionId FK
        string externalId
        string vendorId
        decimal totalAmount
        date issueDate
        date dueDate
        enum status
        datetime createdAt
        datetime updatedAt
    }
    
    AccountingPlatformIntegration ||--o{ RequestLog : "logs"
    AccountingPlatformIntegration ||--o{ UnifiedVendor : "contains"
    AccountingPlatformIntegration ||--o{ UnifiedBill : "contains"
    UnifiedVendor ||--o{ UnifiedBill : "bills"
```

## Error Handling Flow

```mermaid
flowchart TD
    A[Error Occurs] --> B{Error Type}
    
    B -->|ApiException| C[Use Existing Structure]
    B -->|Validation Error| D[Convert to ApiException]
    B -->|Token Error| E[Handle Auth Errors]
    B -->|Database Error| F[Handle DB Errors]
    B -->|Network Error| G[Handle Network Errors]
    B -->|Unknown Error| H[Create Generic ApiException]
    
    C --> I[Global Error Handler]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[Log Error Details]
    I --> K[Sanitize Error Response]
    I --> L[Set HTTP Status Code]
    I --> M[Format Error Response]
    
    M --> N{Response Headers Sent?}
    N -->|No| O[Send Error Response]
    N -->|Yes| P[Log Error Only]
    
    style A fill:#ffcdd2
    style I fill:#fff3e0
    style O fill:#e8f5e8
```

## Kafka Message Flow Architecture

```mermaid
graph LR
    subgraph "Message Producers"
        A[Entity Create Producer]
        B[Sync Completion Producer]
        C[Batch Stream Producer]
    end
    
    subgraph "Kafka Topics"
        D[entity-create-request]
        E[entity-create-response]
        F[entity-batch-stream]
        G[sync-completion-response]
    end
    
    subgraph "Message Consumers"
        H[Entity Create Consumer]
        I[Batch Stream Consumer]
    end
    
    subgraph "Processing Services"
        J[Validation Service]
        K[Mapping Service]
        L[API Gateway Service]
        M[Database Service]
    end
    
    A --> D
    A --> E
    B --> G
    C --> F
    
    D --> H
    F --> I
    
    H --> J
    H --> K
    H --> L
    H --> M
    
    I --> J
    I --> K
    I --> L
    I --> M
    
    style D fill:#e3f2fd
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fce4ec
```

This document provides visual representations of the key architectural components and flows in the Zact Unified Backend system.
